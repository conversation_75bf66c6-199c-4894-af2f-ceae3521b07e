# Required: Your OpenAI API key
OPENAI_API_KEY="ofGALCCf1AewBMLCifxlot4Xp3vmdxFl"

# Optional: Expected Anthropic API key for client validation
# If set, clients must provide this exact API key to access the proxy
ANTHROPIC_API_KEY="your-expected-anthropic-api-key"

# Optional: OpenAI API base URL (default: https://api.openai.com/v1)
# You can change this to use other providers like Azure OpenAI, local models, etc.
OPENAI_BASE_URL="https://antchat.alipay.com/v1"

# Optional: Model mappings (BIG and SMALL models)
BIG_MODEL="Kimi-K2-Instruct"
# Used for Claude opus requests
MIDDLE_MODEL="Kimi-K2-Instruct"
# Used for Claude sonnet requests
SMALL_MODEL="Kimi-K2-Instruct" 
# Used for Claude haiku requests

# Optional: Server settings
HOST="0.0.0.0"
PORT="8082"
LOG_LEVEL="INFO"  
# DEBUG, INFO, WARNING, ERROR, CRITICAL

# Optional: Performance settings  
MAX_TOKENS_LIMIT="4096"
# Minimum tokens limit for requests (to avoid errors with thinking model)
MIN_TOKENS_LIMIT="4096"
REQUEST_TIMEOUT="90"
MAX_RETRIES="2"

# Examples for other providers:

# For Azure OpenAI (recommended if OpenAI is not available in your region):
# OPENAI_API_KEY="your-azure-api-key"
# OPENAI_BASE_URL="https://your-resource-name.openai.azure.com/openai/deployments/your-deployment-name"
# AZURE_API_VERSION="2024-03-01-preview"
# BIG_MODEL="gpt-4"
# MIDDLE_MODEL="gpt-4"
# SMALL_MODEL="gpt-35-turbo"

# For local models (like Ollama):
# OPENAI_API_KEY="dummy-key"  # Required but can be any value for local models
# OPENAI_BASE_URL="http://localhost:11434/v1"
# BIG_MODEL="llama3.1:70b"
# MIDDLE_MODEL="llama3.1:70b"
# SMALL_MODEL="llama3.1:8b"

# Note: If you get "unsupported_country_region_territory" errors,
# consider using Azure OpenAI or a local model setup instead.
