{"type": "streaming_response", "chunks": ["event: message_start\ndata: {\"type\": \"message_start\", \"message\": {\"id\": \"msg_a241936784154c048552c227\", \"type\": \"message\", \"role\": \"assistant\", \"model\": \"claude-3-5-haiku-20241022\", \"content\": [], \"stop_reason\": null, \"stop_sequence\": null, \"usage\": {\"input_tokens\": 0, \"output_tokens\": 0}}}\n\n", "event: content_block_start\ndata: {\"type\": \"content_block_start\", \"index\": 0, \"content_block\": {\"type\": \"text\", \"text\": \"\"}}\n\n", "event: ping\ndata: {\"type\": \"ping\"}\n\n", "event: content_block_delta\ndata: {\"type\": \"content_block_delta\", \"index\": 0, \"delta\": {\"type\": \"text_delta\", \"text\": \"\"}}\n\n", "event: content_block_delta\ndata: {\"type\": \"content_block_delta\", \"index\": 0, \"delta\": {\"type\": \"text_delta\", \"text\": \"Add\"}}\n\n", "event: content_block_delta\ndata: {\"type\": \"content_block_delta\", \"index\": 0, \"delta\": {\"type\": \"text_delta\", \"text\": \" build\"}}\n\n", "event: content_block_delta\ndata: {\"type\": \"content_block_delta\", \"index\": 0, \"delta\": {\"type\": \"text_delta\", \"text\": \" index\"}}\n\n", "event: content_block_delta\ndata: {\"type\": \"content_block_delta\", \"index\": 0, \"delta\": {\"type\": \"text_delta\", \"text\": \" status\"}}\n\n", "event: content_block_delta\ndata: {\"type\": \"content_block_delta\", \"index\": 0, \"delta\": {\"type\": \"text_delta\", \"text\": \" query\"}}\n\n", "event: content_block_delta\ndata: {\"type\": \"content_block_delta\", \"index\": 0, \"delta\": {\"type\": \"text_delta\", \"text\": \" API\"}}\n\n", "event: content_block_delta\ndata: {\"type\": \"content_block_delta\", \"index\": 0, \"delta\": {\"type\": \"text_delta\", \"text\": \"\"}}\n\n", "event: content_block_stop\ndata: {\"type\": \"content_block_stop\", \"index\": 0}\n\n", "event: message_delta\ndata: {\"type\": \"message_delta\", \"delta\": {\"stop_reason\": \"end_turn\", \"stop_sequence\": null}, \"usage\": {\"input_tokens\": 1810, \"output_tokens\": 7, \"cache_read_input_tokens\": 3}}\n\n", "event: message_stop\ndata: {\"type\": \"message_stop\"}\n\n"], "total_chunks": 14}