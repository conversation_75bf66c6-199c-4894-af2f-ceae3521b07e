{"type": "streaming_response", "chunks": ["event: message_start\ndata: {\"type\": \"message_start\", \"message\": {\"id\": \"msg_68daaa6a54eb46f09842ec1b\", \"type\": \"message\", \"role\": \"assistant\", \"model\": \"claude-sonnet-4-20250514\", \"content\": [], \"stop_reason\": null, \"stop_sequence\": null, \"usage\": {\"input_tokens\": 0, \"output_tokens\": 0}}}\n\n", "event: content_block_start\ndata: {\"type\": \"content_block_start\", \"index\": 0, \"content_block\": {\"type\": \"text\", \"text\": \"\"}}\n\n", "event: ping\ndata: {\"type\": \"ping\"}\n\n", "event: error\ndata: {\"type\": \"error\", \"error\": {\"type\": \"api_error\", \"message\": \"Streaming error: argument of type 'NoneType' is not iterable\"}}\n\n"], "total_chunks": 4}